2025-08-05 11:09:42.104 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:09:42.132 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:946)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$InstanceDeclarationElement.processAttribute(FXMLLoader.java:983)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$Element.processStartElement(FXMLLoader.java:230)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:757)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalAccessException: class javafx.fxml.FXMLLoader$ValueElement (in module javafx.fxml) cannot access class com.logictrue.controller.MainController (in module com.logictrue) because module com.logictrue does not export com.logictrue.controller to module javafx.fxml
	at java.base/jdk.internal.reflect.Reflection.newIllegalAccessException(Reflection.java:394)
	at java.base/java.lang.reflect.AccessibleObject.checkAccess(AccessibleObject.java:714)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:495)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:941)
	... 17 common frames omitted
2025-08-05 11:10:55.943 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:10:56.027 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2700)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field private javafx.scene.layout.StackPane com.logictrue.controller.MainController.backgroundPane accessible: module com.logictrue does not "opens com.logictrue.controller" to module javafx.fxml
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:183)
	at java.base/java.lang.reflect.Field.setAccessible(Field.java:177)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleFields(FXMLLoader.java:3620)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3585)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3581)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:319)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleMembers(FXMLLoader.java:3580)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.getControllerFields(FXMLLoader.java:3518)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.injectFields(FXMLLoader.java:1171)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processValue(FXMLLoader.java:870)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:764)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	... 12 common frames omitted
2025-08-05 11:18:24.251 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:18:24.593 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功
2025-08-05 11:18:24.594 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 创建默认配置文件
2025-08-05 11:18:24.825 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:18:24.825 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:18:24.860 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:18:25.083 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:18:28.980 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:18:47.680 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 11:18:47.681 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-05 11:20:45.546 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:20:45.950 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-05 11:20:46.192 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:20:46.192 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:20:46.225 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:20:46.446 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:20:48.350 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:20:53.034 [JavaFX Application Thread] INFO  c.l.controller.FormController - 表单界面初始化完成
2025-08-05 11:21:16.762 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:21:47.329 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:22:17.896 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:22:48.456 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:23:18.999 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:23:49.642 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:24:20.208 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:24:50.763 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:25:21.335 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:25:51.883 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:21.883 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:57.388 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:26:57.782 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 加载配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
 at [Source: (File); line: 1, column: 1]
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:62)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:269)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.createBeanDeserializer(BeanDeserializerFactory.java:151)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer2(DeserializerCache.java:415)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer(DeserializerCache.java:350)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:264)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findContextualValueDeserializer(DeserializationContext.java:621)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:188)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.handlePrimaryContextualization(DeserializationContext.java:836)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerBase.resolve(BeanDeserializerBase.java:550)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:294)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findRootValueDeserializer(DeserializationContext.java:654)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._findRootDeserializer(ObjectMapper.java:4956)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4826)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3637)
	at com.logictrue/com.logictrue.config.ConfigManager.loadConfig(ConfigManager.java:50)
	at com.logictrue/com.logictrue.config.ConfigManager.<init>(ConfigManager.java:28)
	at com.logictrue/com.logictrue.config.ConfigManager.getInstance(ConfigManager.java:33)
	at com.logictrue/com.logictrue.controller.MainController.initialize(MainController.java:48)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2670)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalArgumentException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:1008)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector._fixAccess(CreatorCollector.java:278)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector.setDefaultCreator(CreatorCollector.java:130)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._addExplicitConstructorCreators(BasicDeserializerFactory.java:438)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._constructDefaultValueInstantiator(BasicDeserializerFactory.java:293)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory.findValueInstantiator(BasicDeserializerFactory.java:222)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:262)
	... 35 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Constructor.checkCanSetAccessible(Constructor.java:194)
	at java.base/java.lang.reflect.Constructor.setAccessible(Constructor.java:187)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:995)
	... 41 common frames omitted
2025-08-05 11:26:58.062 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:26:58.062 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:26:58.104 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:58.344 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:27:26.613 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:27:28.064 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:27:35.696 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 11:27:35.698 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-05 11:30:53.906 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:30:54.277 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 加载配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
 at [Source: (File); line: 1, column: 1]
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:62)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:269)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.createBeanDeserializer(BeanDeserializerFactory.java:151)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer2(DeserializerCache.java:415)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer(DeserializerCache.java:350)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:264)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findContextualValueDeserializer(DeserializationContext.java:621)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:188)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.handlePrimaryContextualization(DeserializationContext.java:836)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerBase.resolve(BeanDeserializerBase.java:550)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:294)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findRootValueDeserializer(DeserializationContext.java:654)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._findRootDeserializer(ObjectMapper.java:4956)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4826)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3637)
	at com.logictrue/com.logictrue.config.ConfigManager.loadConfig(ConfigManager.java:50)
	at com.logictrue/com.logictrue.config.ConfigManager.<init>(ConfigManager.java:28)
	at com.logictrue/com.logictrue.config.ConfigManager.getInstance(ConfigManager.java:33)
	at com.logictrue/com.logictrue.controller.MainController.initialize(MainController.java:48)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2670)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalArgumentException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:1008)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector._fixAccess(CreatorCollector.java:278)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector.setDefaultCreator(CreatorCollector.java:130)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._addExplicitConstructorCreators(BasicDeserializerFactory.java:438)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._constructDefaultValueInstantiator(BasicDeserializerFactory.java:293)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory.findValueInstantiator(BasicDeserializerFactory.java:222)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:262)
	... 35 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Constructor.checkCanSetAccessible(Constructor.java:194)
	at java.base/java.lang.reflect.Constructor.setAccessible(Constructor.java:187)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:995)
	... 41 common frames omitted
2025-08-05 11:30:54.495 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:30:54.496 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:30:54.536 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:30:54.777 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:30:58.184 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:31:25.036 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:31:55.595 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:32:26.161 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:32:56.161 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:33:26.727 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
