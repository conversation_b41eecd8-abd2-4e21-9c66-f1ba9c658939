2025-08-05 11:09:42.104 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:09:42.132 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:946)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$InstanceDeclarationElement.processAttribute(FXMLLoader.java:983)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$Element.processStartElement(FXMLLoader.java:230)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:757)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalAccessException: class javafx.fxml.FXMLLoader$ValueElement (in module javafx.fxml) cannot access class com.logictrue.controller.MainController (in module com.logictrue) because module com.logictrue does not export com.logictrue.controller to module javafx.fxml
	at java.base/jdk.internal.reflect.Reflection.newIllegalAccessException(Reflection.java:394)
	at java.base/java.lang.reflect.AccessibleObject.checkAccess(AccessibleObject.java:714)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:495)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:941)
	... 17 common frames omitted
2025-08-05 11:10:55.943 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:10:56.027 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2700)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field private javafx.scene.layout.StackPane com.logictrue.controller.MainController.backgroundPane accessible: module com.logictrue does not "opens com.logictrue.controller" to module javafx.fxml
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:183)
	at java.base/java.lang.reflect.Field.setAccessible(Field.java:177)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleFields(FXMLLoader.java:3620)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3585)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3581)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:319)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleMembers(FXMLLoader.java:3580)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.getControllerFields(FXMLLoader.java:3518)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.injectFields(FXMLLoader.java:1171)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processValue(FXMLLoader.java:870)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:764)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	... 12 common frames omitted
