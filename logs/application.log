2025-08-05 11:09:42.104 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:09:42.132 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:946)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$InstanceDeclarationElement.processAttribute(FXMLLoader.java:983)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$Element.processStartElement(FXMLLoader.java:230)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:757)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalAccessException: class javafx.fxml.FXMLLoader$ValueElement (in module javafx.fxml) cannot access class com.logictrue.controller.MainController (in module com.logictrue) because module com.logictrue does not export com.logictrue.controller to module javafx.fxml
	at java.base/jdk.internal.reflect.Reflection.newIllegalAccessException(Reflection.java:394)
	at java.base/java.lang.reflect.AccessibleObject.checkAccess(AccessibleObject.java:714)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:495)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processAttribute(FXMLLoader.java:941)
	... 17 common frames omitted
2025-08-05 11:10:55.943 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:10:56.027 [JavaFX Application Thread] ERROR com.logictrue.App - 启动应用程序失败
javafx.fxml.LoadException: 
/home/<USER>/nl-mes/iot-jfx/target/classes/fxml/main.fxml:10

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2722)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2700)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field private javafx.scene.layout.StackPane com.logictrue.controller.MainController.backgroundPane accessible: module com.logictrue does not "opens com.logictrue.controller" to module javafx.fxml
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Field.checkCanSetAccessible(Field.java:183)
	at java.base/java.lang.reflect.Field.setAccessible(Field.java:177)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleFields(FXMLLoader.java:3620)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3585)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor$1.run(FXMLLoader.java:3581)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:319)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.addAccessibleMembers(FXMLLoader.java:3580)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ControllerAccessor.getControllerFields(FXMLLoader.java:3518)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.injectFields(FXMLLoader.java:1171)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processValue(FXMLLoader.java:870)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader$ValueElement.processStartElement(FXMLLoader.java:764)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2853)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	... 12 common frames omitted
2025-08-05 11:18:24.251 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:18:24.593 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功
2025-08-05 11:18:24.594 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 创建默认配置文件
2025-08-05 11:18:24.825 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:18:24.825 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:18:24.860 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:18:25.083 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:18:28.980 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:18:47.680 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 11:18:47.681 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-05 11:20:45.546 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:20:45.950 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-05 11:20:46.192 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:20:46.192 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:20:46.225 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:20:46.446 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:20:48.350 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:20:53.034 [JavaFX Application Thread] INFO  c.l.controller.FormController - 表单界面初始化完成
2025-08-05 11:21:16.762 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:21:47.329 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:22:17.896 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:22:48.456 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:23:18.999 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:23:49.642 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:24:20.208 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:24:50.763 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:25:21.335 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:25:51.883 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:21.883 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:57.388 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:26:57.782 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 加载配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
 at [Source: (File); line: 1, column: 1]
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:62)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:269)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.createBeanDeserializer(BeanDeserializerFactory.java:151)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer2(DeserializerCache.java:415)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer(DeserializerCache.java:350)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:264)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findContextualValueDeserializer(DeserializationContext.java:621)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:188)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.handlePrimaryContextualization(DeserializationContext.java:836)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerBase.resolve(BeanDeserializerBase.java:550)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:294)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findRootValueDeserializer(DeserializationContext.java:654)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._findRootDeserializer(ObjectMapper.java:4956)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4826)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3637)
	at com.logictrue/com.logictrue.config.ConfigManager.loadConfig(ConfigManager.java:50)
	at com.logictrue/com.logictrue.config.ConfigManager.<init>(ConfigManager.java:28)
	at com.logictrue/com.logictrue.config.ConfigManager.getInstance(ConfigManager.java:33)
	at com.logictrue/com.logictrue.controller.MainController.initialize(MainController.java:48)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2670)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalArgumentException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:1008)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector._fixAccess(CreatorCollector.java:278)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector.setDefaultCreator(CreatorCollector.java:130)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._addExplicitConstructorCreators(BasicDeserializerFactory.java:438)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._constructDefaultValueInstantiator(BasicDeserializerFactory.java:293)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory.findValueInstantiator(BasicDeserializerFactory.java:222)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:262)
	... 35 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Constructor.checkCanSetAccessible(Constructor.java:194)
	at java.base/java.lang.reflect.Constructor.setAccessible(Constructor.java:187)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:995)
	... 41 common frames omitted
2025-08-05 11:26:58.062 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:26:58.062 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:26:58.104 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:26:58.344 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:27:26.613 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:27:28.064 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:27:35.696 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 11:27:35.698 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-05 11:30:53.906 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:30:54.277 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 加载配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
 at [Source: (File); line: 1, column: 1]
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:62)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:269)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.createBeanDeserializer(BeanDeserializerFactory.java:151)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer2(DeserializerCache.java:415)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer(DeserializerCache.java:350)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:264)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findContextualValueDeserializer(DeserializationContext.java:621)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:188)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.handlePrimaryContextualization(DeserializationContext.java:836)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerBase.resolve(BeanDeserializerBase.java:550)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:294)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findRootValueDeserializer(DeserializationContext.java:654)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._findRootDeserializer(ObjectMapper.java:4956)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4826)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3637)
	at com.logictrue/com.logictrue.config.ConfigManager.loadConfig(ConfigManager.java:50)
	at com.logictrue/com.logictrue.config.ConfigManager.<init>(ConfigManager.java:28)
	at com.logictrue/com.logictrue.config.ConfigManager.getInstance(ConfigManager.java:33)
	at com.logictrue/com.logictrue.controller.MainController.initialize(MainController.java:48)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2670)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalArgumentException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:1008)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector._fixAccess(CreatorCollector.java:278)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector.setDefaultCreator(CreatorCollector.java:130)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._addExplicitConstructorCreators(BasicDeserializerFactory.java:438)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._constructDefaultValueInstantiator(BasicDeserializerFactory.java:293)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory.findValueInstantiator(BasicDeserializerFactory.java:222)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:262)
	... 35 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Constructor.checkCanSetAccessible(Constructor.java:194)
	at java.base/java.lang.reflect.Constructor.setAccessible(Constructor.java:187)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:995)
	... 41 common frames omitted
2025-08-05 11:30:54.495 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:30:54.496 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:30:54.536 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:30:54.777 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:30:58.184 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:31:25.036 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:31:55.595 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:32:26.161 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:32:56.161 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:33:26.727 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:33:57.296 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:34:27.889 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:34:58.455 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:35:29.037 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:35:59.606 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:36:30.201 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:37:00.778 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:37:31.363 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:38:01.936 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:38:32.508 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:39:00.890 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 11:39:01.261 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 加载配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
 at [Source: (File); line: 1, column: 1]
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:62)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:269)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.createBeanDeserializer(BeanDeserializerFactory.java:151)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer2(DeserializerCache.java:415)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createDeserializer(DeserializerCache.java:350)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:264)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findContextualValueDeserializer(DeserializationContext.java:621)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:188)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.createContextual(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.handlePrimaryContextualization(DeserializationContext.java:836)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerBase.resolve(BeanDeserializerBase.java:550)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCache2(DeserializerCache.java:294)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache._createAndCacheValueDeserializer(DeserializerCache.java:244)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.DeserializerCache.findValueDeserializer(DeserializerCache.java:142)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.DeserializationContext.findRootValueDeserializer(DeserializationContext.java:654)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._findRootDeserializer(ObjectMapper.java:4956)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4826)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3637)
	at com.logictrue/com.logictrue.config.ConfigManager.loadConfig(ConfigManager.java:50)
	at com.logictrue/com.logictrue.config.ConfigManager.<init>(ConfigManager.java:28)
	at com.logictrue/com.logictrue.config.ConfigManager.getInstance(ConfigManager.java:33)
	at com.logictrue/com.logictrue.controller.MainController.initialize(MainController.java:48)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2670)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue/com.logictrue.App.start(App.java:28)
	at javafx.graphics@21.0.8/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.8/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.IllegalArgumentException: Failed to call `setAccess()` on Constructor 'com.logictrue.model.FormField' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:1008)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector._fixAccess(CreatorCollector.java:278)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.impl.CreatorCollector.setDefaultCreator(CreatorCollector.java:130)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._addExplicitConstructorCreators(BasicDeserializerFactory.java:438)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory._constructDefaultValueInstantiator(BasicDeserializerFactory.java:293)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BasicDeserializerFactory.findValueInstantiator(BasicDeserializerFactory.java:222)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.deser.BeanDeserializerFactory.buildBeanDeserializer(BeanDeserializerFactory.java:262)
	... 35 common frames omitted
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make public com.logictrue.model.FormField() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind
	at java.base/java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:391)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:367)
	at java.base/java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:315)
	at java.base/java.lang.reflect.Constructor.checkCanSetAccessible(Constructor.java:194)
	at java.base/java.lang.reflect.Constructor.setAccessible(Constructor.java:187)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.util.ClassUtil.checkAndFixAccess(ClassUtil.java:995)
	... 41 common frames omitted
2025-08-05 11:39:01.498 [JavaFX Application Thread] WARN  c.l.controller.MainController - 默认背景图片不存在
2025-08-05 11:39:01.499 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 11:39:01.540 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:39:01.764 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 11:39:03.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 11:39:19.282 [JavaFX Application Thread] INFO  c.l.controller.FormFieldEditDialog - 表单字段保存成功: FormField{id='5d80bd48-9348-4085-9c4d-9d56f0c1e9b8', label='cheh', name='ch', type=TEXT}
2025-08-05 11:39:28.312 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setDeviceId(ConfigManager.java:97)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:284)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.316 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setFormName(ConfigManager.java:106)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:285)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.319 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setApiUrl(ConfigManager.java:115)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:286)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.321 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setHeartbeatUrl(ConfigManager.java:124)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:287)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.323 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->java.util.ArrayList[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setImageUrl(ConfigManager.java:133)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:288)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.330 [JavaFX Application Thread] ERROR com.logictrue.config.ConfigManager - 保存配置文件失败
com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Invalid type definition for type `com.logictrue.model.FormField`: Failed to construct BeanSerializer for [simple type, class com.logictrue.model.FormField]: (java.lang.IllegalArgumentException) Failed to call `setAccess()` on Method 'getId' (of class `com.logictrue.model.FormField`) due to `java.lang.reflect.InaccessibleObjectException`, problem: Unable to make public java.lang.String com.logictrue.model.FormField.getId() accessible: module com.logictrue does not "exports com.logictrue.model" to module com.fasterxml.jackson.databind (through reference chain: com.logictrue.config.AppConfig["formFields"]->com.sun.javafx.collections.ObservableListWrapper[0])
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.exc.InvalidDefinitionException.from(InvalidDefinitionException.java:72)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.reportBadTypeDefinition(SerializerProvider.java:1280)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.constructBeanOrAddOnSerializer(BeanSerializerFactory.java:475)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.findBeanOrAddOnSerializer(BeanSerializerFactory.java:295)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory._createSerializer2(BeanSerializerFactory.java:240)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializerFactory.createSerializer(BeanSerializerFactory.java:174)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createUntypedSerializer(SerializerProvider.java:1503)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider._createAndCacheUntypedSerializer(SerializerProvider.java:1451)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.SerializerProvider.findContentValueSerializer(SerializerProvider.java:789)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.PropertySerializerMap.findAndAddSecondarySerializer(PropertySerializerMap.java:90)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.AsArraySerializerBase._findAndAddDynamic(AsArraySerializerBase.java:314)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:115)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:772)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:178)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:479)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:318)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1572)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter._writeValueAndClose(ObjectWriter.java:1273)
	at com.fasterxml.jackson.databind@2.15.2/com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1081)
	at com.logictrue/com.logictrue.config.ConfigManager.saveConfig(ConfigManager.java:74)
	at com.logictrue/com.logictrue.config.ConfigManager.setFormFields(ConfigManager.java:151)
	at com.logictrue/com.logictrue.controller.SettingsController.saveSettings(SettingsController.java:291)
	at com.logictrue/com.logictrue.controller.SettingsController.lambda$initializeUI$2(SettingsController.java:131)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.enterNestedEventLoopImpl(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._enterNestedEventLoop(GtkApplication.java:333)
	at javafx.graphics@21.0.8/com.sun.glass.ui.Application.enterNestedEventLoop(Application.java:515)
	at javafx.graphics@21.0.8/com.sun.glass.ui.EventLoop.enter(EventLoop.java:107)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.enterNestedEventLoop(QuantumToolkit.java:650)
	at javafx.graphics@21.0.8/javafx.stage.Stage.showAndWait(Stage.java:469)
	at com.logictrue/com.logictrue.controller.MainController.openSettingsWindow(MainController.java:141)
	at com.logictrue/com.logictrue.controller.MainController.lambda$initializeUI$0(MainController.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-05 11:39:28.331 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置保存成功
2025-08-05 11:39:30.583 [JavaFX Application Thread] INFO  c.l.controller.DynamicFormController - 动态表单界面初始化完成
2025-08-05 11:39:32.097 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
org.apache.http.conn.HttpHostConnectException: Connect to localhost:8080 [localhost/127.0.0.1] failed: Connection refused
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.logictrue/com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:105)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: Connection refused
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.httpcomponents.httpclient@4.5.14/org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 17 common frames omitted
2025-08-05 11:39:39.028 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 11:39:39.030 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
