package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.FormField;
import com.logictrue.service.NetworkService;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * 设置界面控制器
 */
public class SettingsController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);
    
    @FXML
    private TextField deviceIdField;
    
    @FXML
    private TextField formNameField;
    
    @FXML
    private TextField apiUrlField;
    
    @FXML
    private TextField heartbeatUrlField;
    
    @FXML
    private TextField imageUrlField;
    
    @FXML
    private Button downloadImageButton;
    
    @FXML
    private Button testHeartbeatButton;
    
    @FXML
    private Button saveButton;
    
    @FXML
    private Button cancelButton;
    
    @FXML
    private ProgressIndicator progressIndicator;
    
    @FXML
    private Label statusLabel;

    // 表单字段管理相关组件
    @FXML
    private TabPane settingsTabPane;

    @FXML
    private TableView<FormField> formFieldsTable;

    @FXML
    private TableColumn<FormField, String> labelColumn;

    @FXML
    private TableColumn<FormField, String> nameColumn;

    @FXML
    private TableColumn<FormField, String> typeColumn;

    @FXML
    private Button addFieldButton;

    @FXML
    private Button editFieldButton;

    @FXML
    private Button deleteFieldButton;

    @FXML
    private Button moveUpButton;

    @FXML
    private Button moveDownButton;

    private ConfigManager configManager;
    private NetworkService networkService;
    private MainController mainController;
    private ObservableList<FormField> formFieldsList;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        networkService = new NetworkService();
        formFieldsList = FXCollections.observableArrayList();

        // 初始化界面
        initializeUI();

        // 初始化表单字段表格
        initializeFormFieldsTable();

        // 加载当前配置
        loadCurrentConfig();

        // 加载表单字段
        loadFormFields();

        logger.info("设置界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);
        
        // 绑定事件
        downloadImageButton.setOnAction(event -> downloadDeviceImage());
        testHeartbeatButton.setOnAction(event -> testHeartbeat());
        saveButton.setOnAction(event -> saveSettings());
        cancelButton.setOnAction(event -> closeWindow());
        
        // 设备ID变化时启用下载按钮
        deviceIdField.textProperty().addListener((observable, oldValue, newValue) -> {
            downloadImageButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });
        
        // 心跳URL变化时启用测试按钮
        heartbeatUrlField.textProperty().addListener((observable, oldValue, newValue) -> {
            testHeartbeatButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });

        // 表单字段管理按钮事件
        addFieldButton.setOnAction(event -> addFormField());
        editFieldButton.setOnAction(event -> editFormField());
        deleteFieldButton.setOnAction(event -> deleteFormField());
        moveUpButton.setOnAction(event -> moveFieldUp());
        moveDownButton.setOnAction(event -> moveFieldDown());
    }
    
    /**
     * 加载当前配置
     */
    private void loadCurrentConfig() {
        deviceIdField.setText(configManager.getDeviceId());
        formNameField.setText(configManager.getFormName());
        apiUrlField.setText(configManager.getApiUrl());
        heartbeatUrlField.setText(configManager.getHeartbeatUrl());
        imageUrlField.setText(configManager.getImageUrl());
        
        // 更新按钮状态
        downloadImageButton.setDisable(deviceIdField.getText().trim().isEmpty());
        testHeartbeatButton.setDisable(heartbeatUrlField.getText().trim().isEmpty());
    }
    
    /**
     * 下载设备图片
     */
    @FXML
    private void downloadDeviceImage() {
        String deviceId = deviceIdField.getText().trim();
        if (deviceId.isEmpty()) {
            showStatus("请先输入设备编号", false);
            return;
        }
        
        // 显示进度指示器
        progressIndicator.setVisible(true);
        downloadImageButton.setDisable(true);
        showStatus("正在下载设备图片...", true);
        
        Task<String> downloadTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                return networkService.downloadDeviceImage(deviceId).get();
            }
        };
        
        downloadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                
                String imagePath = downloadTask.getValue();
                if (imagePath != null) {
                    configManager.setBackgroundImagePath(imagePath);
                    showStatus("设备图片下载成功", true);
                    
                    // 通知主界面更新背景图片
                    if (mainController != null) {
                        mainController.updateBackgroundImage(imagePath);
                    }
                } else {
                    showStatus("设备图片下载失败", false);
                }
            });
        });
        
        downloadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                showStatus("设备图片下载失败: " + downloadTask.getException().getMessage(), false);
            });
        });
        
        Thread downloadThread = new Thread(downloadTask);
        downloadThread.setDaemon(true);
        downloadThread.start();
    }
    
    /**
     * 测试心跳连接
     */
    @FXML
    private void testHeartbeat() {
        String heartbeatUrl = heartbeatUrlField.getText().trim();
        if (heartbeatUrl.isEmpty()) {
            showStatus("请先输入心跳地址", false);
            return;
        }
        
        // 临时更新配置以进行测试
        String originalUrl = configManager.getHeartbeatUrl();
        configManager.setHeartbeatUrl(heartbeatUrl);
        
        progressIndicator.setVisible(true);
        testHeartbeatButton.setDisable(true);
        showStatus("正在测试心跳连接...", true);
        
        networkService.sendHeartbeat().thenAccept(result -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                
                if (result.isSuccess()) {
                    showStatus(String.format("心跳测试成功 (响应时间: %dms)", result.getResponseTime()), true);
                } else {
                    showStatus(String.format("心跳测试失败 (状态码: %d)", result.getStatusCode()), false);
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                showStatus("心跳测试异常: " + throwable.getMessage(), false);
            });
            return null;
        });
        
        // 恢复原始配置
        configManager.setHeartbeatUrl(originalUrl);
    }
    
    /**
     * 保存设置
     */
    @FXML
    private void saveSettings() {
        try {
            // 验证输入
            if (formNameField.getText().trim().isEmpty()) {
                showStatus("表单名称不能为空", false);
                return;
            }
            
            if (apiUrlField.getText().trim().isEmpty()) {
                showStatus("接口地址不能为空", false);
                return;
            }
            
            // 保存配置
            configManager.setDeviceId(deviceIdField.getText().trim());
            configManager.setFormName(formNameField.getText().trim());
            configManager.setApiUrl(apiUrlField.getText().trim());
            configManager.setHeartbeatUrl(heartbeatUrlField.getText().trim());
            configManager.setImageUrl(imageUrlField.getText().trim());

            // 保存表单字段配置
            configManager.setFormFields(formFieldsList);
            
            showStatus("设置保存成功", true);
            logger.info("设置保存成功");
            
            // 延迟关闭窗口
            Platform.runLater(() -> {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                closeWindow();
            });
            
        } catch (Exception e) {
            logger.error("保存设置失败", e);
            showStatus("保存设置失败: " + e.getMessage(), false);
        }
    }
    
    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }
    
    /**
     * 初始化表单字段表格
     */
    private void initializeFormFieldsTable() {
        // 设置表格列
        labelColumn.setCellValueFactory(new PropertyValueFactory<>("label"));
        nameColumn.setCellValueFactory(new PropertyValueFactory<>("name"));
        typeColumn.setCellValueFactory(cellData ->
            new SimpleStringProperty(cellData.getValue().getType().getDisplayName()));

        // 设置表格数据
        formFieldsTable.setItems(formFieldsList);

        // 表格选择事件
        formFieldsTable.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            boolean hasSelection = newValue != null;
            editFieldButton.setDisable(!hasSelection);
            deleteFieldButton.setDisable(!hasSelection);
            moveUpButton.setDisable(!hasSelection);
            moveDownButton.setDisable(!hasSelection);
        });
    }

    /**
     * 加载表单字段
     */
    private void loadFormFields() {
        List<FormField> fields = configManager.getFormFields();
        formFieldsList.clear();
        formFieldsList.addAll(fields);
    }

    /**
     * 新增表单字段
     */
    private void addFormField() {
        FormFieldEditDialog dialog = new FormFieldEditDialog(null);
        if (dialog.showAndWait()) {
            FormField newField = dialog.getFormField();
            formFieldsList.add(newField);
            showStatus("字段添加成功", true);
        }
    }

    /**
     * 编辑表单字段
     */
    private void editFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            FormFieldEditDialog dialog = new FormFieldEditDialog(selectedField);
            if (dialog.showAndWait()) {
                // 刷新表格显示
                formFieldsTable.refresh();
                showStatus("字段编辑成功", true);
            }
        }
    }

    /**
     * 删除表单字段
     */
    private void deleteFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认删除");
            alert.setHeaderText(null);
            alert.setContentText("确定要删除字段 \"" + selectedField.getLabel() + "\" 吗？");

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                formFieldsList.remove(selectedField);
                showStatus("字段删除成功", true);
            }
        }
    }

    /**
     * 上移字段
     */
    private void moveFieldUp() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex > 0) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex - 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex - 1);
                showStatus("字段上移成功", true);
            }
        }
    }

    /**
     * 下移字段
     */
    private void moveFieldDown() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex < formFieldsList.size() - 1) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex + 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex + 1);
                showStatus("字段下移成功", true);
            }
        }
    }



    /**
     * 设置主控制器引用
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }
}
