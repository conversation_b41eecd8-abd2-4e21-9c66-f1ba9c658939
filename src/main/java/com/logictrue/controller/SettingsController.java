package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.service.NetworkService;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * 设置界面控制器
 */
public class SettingsController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);
    
    @FXML
    private TextField deviceIdField;
    
    @FXML
    private TextField formNameField;
    
    @FXML
    private TextField apiUrlField;
    
    @FXML
    private TextField heartbeatUrlField;
    
    @FXML
    private TextField imageUrlField;
    
    @FXML
    private Button downloadImageButton;
    
    @FXML
    private Button testHeartbeatButton;
    
    @FXML
    private Button saveButton;
    
    @FXML
    private Button cancelButton;
    
    @FXML
    private ProgressIndicator progressIndicator;
    
    @FXML
    private Label statusLabel;
    
    private ConfigManager configManager;
    private NetworkService networkService;
    private MainController mainController;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        networkService = new NetworkService();
        
        // 初始化界面
        initializeUI();
        
        // 加载当前配置
        loadCurrentConfig();
        
        logger.info("设置界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);
        
        // 绑定事件
        downloadImageButton.setOnAction(event -> downloadDeviceImage());
        testHeartbeatButton.setOnAction(event -> testHeartbeat());
        saveButton.setOnAction(event -> saveSettings());
        cancelButton.setOnAction(event -> closeWindow());
        
        // 设备ID变化时启用下载按钮
        deviceIdField.textProperty().addListener((observable, oldValue, newValue) -> {
            downloadImageButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });
        
        // 心跳URL变化时启用测试按钮
        heartbeatUrlField.textProperty().addListener((observable, oldValue, newValue) -> {
            testHeartbeatButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });
    }
    
    /**
     * 加载当前配置
     */
    private void loadCurrentConfig() {
        deviceIdField.setText(configManager.getDeviceId());
        formNameField.setText(configManager.getFormName());
        apiUrlField.setText(configManager.getApiUrl());
        heartbeatUrlField.setText(configManager.getHeartbeatUrl());
        imageUrlField.setText(configManager.getImageUrl());
        
        // 更新按钮状态
        downloadImageButton.setDisable(deviceIdField.getText().trim().isEmpty());
        testHeartbeatButton.setDisable(heartbeatUrlField.getText().trim().isEmpty());
    }
    
    /**
     * 下载设备图片
     */
    @FXML
    private void downloadDeviceImage() {
        String deviceId = deviceIdField.getText().trim();
        if (deviceId.isEmpty()) {
            showStatus("请先输入设备编号", false);
            return;
        }
        
        // 显示进度指示器
        progressIndicator.setVisible(true);
        downloadImageButton.setDisable(true);
        showStatus("正在下载设备图片...", true);
        
        Task<String> downloadTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                return networkService.downloadDeviceImage(deviceId).get();
            }
        };
        
        downloadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                
                String imagePath = downloadTask.getValue();
                if (imagePath != null) {
                    configManager.setBackgroundImagePath(imagePath);
                    showStatus("设备图片下载成功", true);
                    
                    // 通知主界面更新背景图片
                    if (mainController != null) {
                        mainController.updateBackgroundImage(imagePath);
                    }
                } else {
                    showStatus("设备图片下载失败", false);
                }
            });
        });
        
        downloadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                showStatus("设备图片下载失败: " + downloadTask.getException().getMessage(), false);
            });
        });
        
        Thread downloadThread = new Thread(downloadTask);
        downloadThread.setDaemon(true);
        downloadThread.start();
    }
    
    /**
     * 测试心跳连接
     */
    @FXML
    private void testHeartbeat() {
        String heartbeatUrl = heartbeatUrlField.getText().trim();
        if (heartbeatUrl.isEmpty()) {
            showStatus("请先输入心跳地址", false);
            return;
        }
        
        // 临时更新配置以进行测试
        String originalUrl = configManager.getHeartbeatUrl();
        configManager.setHeartbeatUrl(heartbeatUrl);
        
        progressIndicator.setVisible(true);
        testHeartbeatButton.setDisable(true);
        showStatus("正在测试心跳连接...", true);
        
        networkService.sendHeartbeat().thenAccept(result -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                
                if (result.isSuccess()) {
                    showStatus(String.format("心跳测试成功 (响应时间: %dms)", result.getResponseTime()), true);
                } else {
                    showStatus(String.format("心跳测试失败 (状态码: %d)", result.getStatusCode()), false);
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                showStatus("心跳测试异常: " + throwable.getMessage(), false);
            });
            return null;
        });
        
        // 恢复原始配置
        configManager.setHeartbeatUrl(originalUrl);
    }
    
    /**
     * 保存设置
     */
    @FXML
    private void saveSettings() {
        try {
            // 验证输入
            if (formNameField.getText().trim().isEmpty()) {
                showStatus("表单名称不能为空", false);
                return;
            }
            
            if (apiUrlField.getText().trim().isEmpty()) {
                showStatus("接口地址不能为空", false);
                return;
            }
            
            // 保存配置
            configManager.setDeviceId(deviceIdField.getText().trim());
            configManager.setFormName(formNameField.getText().trim());
            configManager.setApiUrl(apiUrlField.getText().trim());
            configManager.setHeartbeatUrl(heartbeatUrlField.getText().trim());
            configManager.setImageUrl(imageUrlField.getText().trim());
            
            showStatus("设置保存成功", true);
            logger.info("设置保存成功");
            
            // 延迟关闭窗口
            Platform.runLater(() -> {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                closeWindow();
            });
            
        } catch (Exception e) {
            logger.error("保存设置失败", e);
            showStatus("保存设置失败: " + e.getMessage(), false);
        }
    }
    
    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }
    
    /**
     * 设置主控制器引用
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }
}
