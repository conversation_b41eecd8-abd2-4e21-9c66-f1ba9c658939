package com.logictrue.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.model.FormField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 配置管理器，负责应用配置的持久化存储
 */
public class ConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static final String CONFIG_FILE = "config.json";
    private static final String CONFIG_DIR = System.getProperty("user.home") + "/.iot-jfx";
    
    private static ConfigManager instance;
    private AppConfig config;
    private ObjectMapper objectMapper;
    
    private ConfigManager() {
        this.objectMapper = new ObjectMapper();
        loadConfig();
    }
    
    public static synchronized ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }
            
            File configFile = new File(configDir, CONFIG_FILE);
            if (configFile.exists()) {
                config = objectMapper.readValue(configFile, AppConfig.class);
                logger.info("配置文件加载成功");
            } else {
                config = new AppConfig();
                saveConfig();
                logger.info("创建默认配置文件");
            }
        } catch (IOException e) {
            logger.error("加载配置文件失败", e);
            config = new AppConfig();
        }
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            File configDir = new File(CONFIG_DIR);
            if (!configDir.exists()) {
                configDir.mkdirs();
            }
            
            File configFile = new File(configDir, CONFIG_FILE);
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(configFile, config);
            logger.info("配置文件保存成功");
        } catch (IOException e) {
            logger.error("保存配置文件失败", e);
        }
    }
    
    public AppConfig getConfig() {
        return config;
    }
    
    public void setConfig(AppConfig config) {
        this.config = config;
        saveConfig();
    }
    
    // 便捷方法
    public String getDeviceId() {
        return config.getDeviceId();
    }
    
    public void setDeviceId(String deviceId) {
        config.setDeviceId(deviceId);
        saveConfig();
    }
    
    public String getFormName() {
        return config.getFormName();
    }
    
    public void setFormName(String formName) {
        config.setFormName(formName);
        saveConfig();
    }
    
    public String getApiUrl() {
        return config.getApiUrl();
    }
    
    public void setApiUrl(String apiUrl) {
        config.setApiUrl(apiUrl);
        saveConfig();
    }
    
    public String getHeartbeatUrl() {
        return config.getHeartbeatUrl();
    }
    
    public void setHeartbeatUrl(String heartbeatUrl) {
        config.setHeartbeatUrl(heartbeatUrl);
        saveConfig();
    }
    
    public String getImageUrl() {
        return config.getImageUrl();
    }
    
    public void setImageUrl(String imageUrl) {
        config.setImageUrl(imageUrl);
        saveConfig();
    }
    
    public String getBackgroundImagePath() {
        return config.getBackgroundImagePath();
    }
    
    public void setBackgroundImagePath(String backgroundImagePath) {
        config.setBackgroundImagePath(backgroundImagePath);
        saveConfig();
    }

    public List<FormField> getFormFields() {
        List<FormField> fields = config.getFormFields();
        logger.info("ConfigManager获取表单字段，共{}个", fields.size());
        return fields;
    }

    public void setFormFields(List<FormField> formFields) {
        config.setFormFields(formFields);
        saveConfig();
    }

    public void addFormField(FormField formField) {
        config.getFormFields().add(formField);
        saveConfig();
    }

    public void removeFormField(String fieldId) {
        config.getFormFields().removeIf(field -> field.getId().equals(fieldId));
        saveConfig();
    }

    public void updateFormField(FormField updatedField) {
        List<FormField> fields = config.getFormFields();
        for (int i = 0; i < fields.size(); i++) {
            if (fields.get(i).getId().equals(updatedField.getId())) {
                fields.set(i, updatedField);
                break;
            }
        }
        saveConfig();
    }
}
