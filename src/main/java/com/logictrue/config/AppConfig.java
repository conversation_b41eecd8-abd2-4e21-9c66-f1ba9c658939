package com.logictrue.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 应用配置数据类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppConfig {
    
    private String deviceId = "";
    private String formName = "数据采集表单";
    private String apiUrl = "http://localhost:8080/api/submit";
    private String heartbeatUrl = "http://localhost:8080/api/heartbeat";
    private String imageUrl = "http://localhost:8080/api/device/image";
    private String backgroundImagePath = "";
    
    public AppConfig() {
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getFormName() {
        return formName;
    }
    
    public void setFormName(String formName) {
        this.formName = formName;
    }
    
    public String getApiUrl() {
        return apiUrl;
    }
    
    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    public String getHeartbeatUrl() {
        return heartbeatUrl;
    }
    
    public void setHeartbeatUrl(String heartbeatUrl) {
        this.heartbeatUrl = heartbeatUrl;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getBackgroundImagePath() {
        return backgroundImagePath;
    }
    
    public void setBackgroundImagePath(String backgroundImagePath) {
        this.backgroundImagePath = backgroundImagePath;
    }
    
    @Override
    public String toString() {
        return "AppConfig{" +
                "deviceId='" + deviceId + '\'' +
                ", formName='" + formName + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", heartbeatUrl='" + heartbeatUrl + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", backgroundImagePath='" + backgroundImagePath + '\'' +
                '}';
    }
}
