package com.logictrue.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.logictrue.model.FormField;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用配置数据类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppConfig {

    private String deviceId = "";
    private String formName = "数据采集表单";
    private String apiUrl = "http://localhost:8080/api/submit";
    private String heartbeatUrl = "http://localhost:8080/api/heartbeat";
    private String imageUrl = "http://localhost:8080/api/device/image";
    private String backgroundImagePath = "";
    private List<FormField> formFields = new ArrayList<>();
    
    public AppConfig() {
        initializeDefaultFormFields();
    }

    /**
     * 初始化默认表单字段
     */
    private void initializeDefaultFormFields() {
        if (formFields.isEmpty()) {
            // 添加默认的表单字段
            formFields.add(new FormField("deviceId", "设备编号", "deviceId", FormField.FieldType.TEXT));
            formFields.add(new FormField("operator", "操作员", "operator", FormField.FieldType.TEXT));
            formFields.add(new FormField("temperature", "温度(°C)", "temperature", FormField.FieldType.NUMBER));
            formFields.add(new FormField("humidity", "湿度(%)", "humidity", FormField.FieldType.NUMBER));
            formFields.add(new FormField("pressure", "压力(Pa)", "pressure", FormField.FieldType.NUMBER));
            formFields.add(new FormField("remarks", "备注", "remarks", FormField.FieldType.TEXTAREA));

            // 设置必填字段
            formFields.get(0).setRequired(true); // 设备编号
            formFields.get(1).setRequired(true); // 操作员

            // 设置占位符
            formFields.get(0).setPlaceholder("请输入设备编号");
            formFields.get(1).setPlaceholder("请输入操作员姓名");
            formFields.get(2).setPlaceholder("请输入温度值");
            formFields.get(3).setPlaceholder("请输入湿度值");
            formFields.get(4).setPlaceholder("请输入压力值");
            formFields.get(5).setPlaceholder("请输入备注信息");

            // 设置显示顺序
            for (int i = 0; i < formFields.size(); i++) {
                formFields.get(i).setDisplayOrder(i);
            }
        }
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getFormName() {
        return formName;
    }
    
    public void setFormName(String formName) {
        this.formName = formName;
    }
    
    public String getApiUrl() {
        return apiUrl;
    }
    
    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    public String getHeartbeatUrl() {
        return heartbeatUrl;
    }
    
    public void setHeartbeatUrl(String heartbeatUrl) {
        this.heartbeatUrl = heartbeatUrl;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getBackgroundImagePath() {
        return backgroundImagePath;
    }
    
    public void setBackgroundImagePath(String backgroundImagePath) {
        this.backgroundImagePath = backgroundImagePath;
    }

    public List<FormField> getFormFields() {
        if (formFields == null) {
            formFields = new ArrayList<>();
            initializeDefaultFormFields();
        }
        return formFields;
    }

    public void setFormFields(List<FormField> formFields) {
        this.formFields = formFields;
    }
    
    @Override
    public String toString() {
        return "AppConfig{" +
                "deviceId='" + deviceId + '\'' +
                ", formName='" + formName + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", heartbeatUrl='" + heartbeatUrl + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", backgroundImagePath='" + backgroundImagePath + '\'' +
                '}';
    }
}
