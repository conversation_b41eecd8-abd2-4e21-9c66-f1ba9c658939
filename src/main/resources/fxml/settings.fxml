<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>

<VBox spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.SettingsController">
   <children>
      <!-- 标题 -->
      <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="系统设置" />
      
      <!-- 设置表单 -->
      <GridPane hgap="10.0" vgap="15.0">
        <columnConstraints>
          <javafx.scene.layout.ColumnConstraints hgrow="NEVER" minWidth="120.0" />
          <javafx.scene.layout.ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
            <javafx.scene.layout.ColumnConstraints hgrow="NEVER" minWidth="100.0" />
        </columnConstraints>
        <rowConstraints>
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
            <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
            <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <!-- 设备编号 -->
            <Label text="设备编号:" />
            <TextField fx:id="deviceIdField" promptText="请输入设备编号" GridPane.columnIndex="1" />
            <Button fx:id="downloadImageButton" disable="true" mnemonicParsing="false" text="下载图片" GridPane.columnIndex="2" />
            
            <!-- 表单名称 -->
            <Label text="表单名称:" GridPane.rowIndex="1" />
            <TextField fx:id="formNameField" promptText="请输入表单名称" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            
            <!-- 接口地址 -->
            <Label text="接口地址:" GridPane.rowIndex="2" />
            <TextField fx:id="apiUrlField" promptText="请输入表单提交接口地址" GridPane.columnIndex="1" GridPane.rowIndex="2" />
            
            <!-- 心跳地址 -->
            <Label text="心跳地址:" GridPane.rowIndex="3" />
            <TextField fx:id="heartbeatUrlField" promptText="请输入心跳检测地址" GridPane.columnIndex="1" GridPane.rowIndex="3" />
            <Button fx:id="testHeartbeatButton" disable="true" mnemonicParsing="false" text="测试连接" GridPane.columnIndex="2" GridPane.rowIndex="3" />
            
            <!-- 图片地址 -->
            <Label text="图片地址:" GridPane.rowIndex="4" />
            <TextField fx:id="imageUrlField" promptText="请输入设备图片下载地址" GridPane.columnIndex="1" GridPane.rowIndex="4" />
         </children>
      </GridPane>
      
      <!-- 状态区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
            <Label fx:id="statusLabel" text="" />
         </children>
      </HBox>
      
      <!-- 按钮区域 -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="saveButton" mnemonicParsing="false" text="保存" />
            <Button fx:id="cancelButton" mnemonicParsing="false" text="取消" />
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
