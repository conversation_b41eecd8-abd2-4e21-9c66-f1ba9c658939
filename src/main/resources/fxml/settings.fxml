<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.SettingsController">
   <children>
      <!-- 标题 -->
      <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="系统设置" />

      <!-- 选项卡面板 -->
      <TabPane fx:id="settingsTabPane" tabClosingPolicy="UNAVAILABLE">
         <tabs>
            <!-- 基本设置选项卡 -->
            <Tab text="基本设置">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 设置表单 -->
                        <GridPane hgap="10.0" vgap="15.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                            <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <!-- 设备编号 -->
                              <Label text="设备编号:" />
                              <TextField fx:id="deviceIdField" promptText="请输入设备编号" GridPane.columnIndex="1" />
                              <Button fx:id="downloadImageButton" disable="true" mnemonicParsing="false" text="下载图片" GridPane.columnIndex="2" />

                              <!-- 表单名称 -->
                              <Label text="表单名称:" GridPane.rowIndex="1" />
                              <TextField fx:id="formNameField" promptText="请输入表单名称" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                              <!-- 接口地址 -->
                              <Label text="接口地址:" GridPane.rowIndex="2" />
                              <TextField fx:id="apiUrlField" promptText="请输入表单提交接口地址" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                              <!-- 心跳地址 -->
                              <Label text="心跳地址:" GridPane.rowIndex="3" />
                              <TextField fx:id="heartbeatUrlField" promptText="请输入心跳检测地址" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                              <Button fx:id="testHeartbeatButton" disable="true" mnemonicParsing="false" text="测试连接" GridPane.columnIndex="2" GridPane.rowIndex="3" />

                              <!-- 图片地址 -->
                              <Label text="图片地址:" GridPane.rowIndex="4" />
                              <TextField fx:id="imageUrlField" promptText="请输入设备图片下载地址" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                           </children>
                        </GridPane>
                     </children>
                  </VBox>
               </content>
            </Tab>

            <!-- 表单字段管理选项卡 -->
            <Tab text="表单字段">
               <content>
                  <VBox spacing="15.0">
                     <padding>
                        <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
                     </padding>
                     <children>
                        <!-- 字段管理工具栏 -->
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="表单字段管理:" style="-fx-font-weight: bold;" />
                              <Button fx:id="addFieldButton" text="新增字段" />
                              <Button fx:id="editFieldButton" text="编辑字段" disable="true" />
                              <Button fx:id="deleteFieldButton" text="删除字段" disable="true" />
                              <Button fx:id="moveUpButton" text="上移" disable="true" />
                              <Button fx:id="moveDownButton" text="下移" disable="true" />
                           </children>
                        </HBox>

                        <!-- 字段列表 -->
                        <TableView fx:id="formFieldsTable" prefHeight="300.0">
                           <columns>
                              <TableColumn fx:id="labelColumn" text="字段标签" prefWidth="200.0" />
                              <TableColumn fx:id="nameColumn" text="字段名称" prefWidth="200.0" />
                              <TableColumn fx:id="typeColumn" text="字段类型" prefWidth="150.0" />
                           </columns>
                        </TableView>
                     </children>
                  </VBox>
               </content>
            </Tab>
         </tabs>
      </TabPane>

      <!-- 状态区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
            <Label fx:id="statusLabel" text="" />
         </children>
      </HBox>

      <!-- 按钮区域 -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="saveButton" mnemonicParsing="false" text="保存" />
            <Button fx:id="cancelButton" mnemonicParsing="false" text="取消" />
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
