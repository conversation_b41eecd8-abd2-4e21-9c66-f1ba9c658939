# IoT数据采集JavaFX应用程序

## 项目概述

这是一个基于JavaFX开发的IoT数据采集应用程序，提供了设备管理、数据采集表单、心跳监控等功能。

## 功能特性

### 1. 首页界面
- 支持动态背景图片（根据设备编号下载）
- 圆形"快速开始"按钮，点击进入表单填写页面
- 设置按钮，点击打开设置界面

### 2. 设置界面
- **设备编号设置**：配置当前设备的唯一标识
- **图片下载功能**：根据设备编号自动下载设备图片并更新首页背景
- **表单配置**：
  - 表单名称设置
  - 表单提交接口地址配置
- **心跳监控配置**：
  - 心跳检测地址设置
  - 心跳连接测试功能
- **图片服务配置**：设备图片下载地址
- 所有配置自动持久化保存

### 3. 表单填写页面
- 动态表单标题（使用设置中的表单名称）
- 基本信息采集：设备编号、操作员
- 传感器数据采集：温度、湿度、压力
- 备注信息输入
- 表单验证和提交功能
- 提交到设置中配置的接口地址

### 4. 心跳监控功能
- 自动启动心跳监控服务
- 每30秒执行一次心跳检测
- 心跳超时时间：5秒
- 心跳结果记录到专用日志文件
- 支持手动测试心跳连接

## 技术架构

### 技术栈
- **Java 11**：开发语言
- **JavaFX 21**：用户界面框架
- **Apache HttpClient**：HTTP请求处理
- **Jackson**：JSON数据处理
- **SLF4J + Logback**：日志框架
- **Maven**：项目构建工具

### 项目结构
```
src/main/java/com/logictrue/
├── App.java                          # 主应用程序类
├── config/
│   ├── AppConfig.java               # 应用配置数据类
│   └── ConfigManager.java           # 配置管理器
├── controller/
│   ├── MainController.java          # 主界面控制器
│   ├── SettingsController.java      # 设置界面控制器
│   └── FormController.java          # 表单界面控制器
└── service/
    ├── NetworkService.java          # 网络服务类
    └── HeartbeatService.java        # 心跳监控服务

src/main/resources/
├── fxml/
│   ├── main.fxml                    # 主界面布局
│   ├── settings.fxml                # 设置界面布局
│   └── form.fxml                    # 表单界面布局
├── css/
│   └── application.css              # 应用程序样式
├── images/
│   └── default-background.jpg       # 默认背景图片
└── logback.xml                      # 日志配置
```

## 配置说明

### 配置文件位置
应用程序配置文件自动保存在用户目录下：
- Windows: `C:\Users\<USER>\.iot-jfx\config.json`
- Linux/Mac: `/home/<USER>/.iot-jfx/config.json`

### 配置项说明
```json
{
  "deviceId": "设备编号",
  "formName": "表单名称",
  "apiUrl": "表单提交接口地址",
  "heartbeatUrl": "心跳检测地址",
  "imageUrl": "设备图片下载地址",
  "backgroundImagePath": "当前背景图片本地路径"
}
```

### 缓存目录
- 下载的设备图片缓存在：`{用户目录}/.iot-jfx/cache/`
- 图片文件命名格式：`device_{设备编号}.jpg`

## 日志说明

### 日志文件
- **应用日志**：`logs/application.log`
- **心跳日志**：`logs/heartbeat.log`

### 日志轮转
- 应用日志：单文件最大10MB，保留30天
- 心跳日志：单文件最大5MB，保留7天

## 运行说明

### 环境要求
- Java 11 或更高版本
- Maven 3.6 或更高版本

### 编译运行
```bash
# 编译项目
mvn clean compile

# 运行应用程序
mvn javafx:run
```

### 打包部署
```bash
# 创建可执行JAR包
mvn clean package

# 运行打包后的应用程序
java -jar target/iot-jfx-1.0-SNAPSHOT.jar
```

## API接口说明

### 心跳接口
- **请求方式**：GET
- **请求地址**：配置中的heartbeatUrl
- **响应要求**：HTTP状态码200表示成功

### 设备图片接口
- **请求方式**：GET
- **请求地址**：`{imageUrl}?deviceId={设备编号}`
- **响应要求**：返回图片文件（JPG/PNG格式）

### 表单提交接口
- **请求方式**：POST
- **请求地址**：配置中的apiUrl
- **请求头**：`Content-Type: application/json`
- **请求体示例**：
```json
{
  "deviceId": "DEVICE001",
  "operator": "张三",
  "temperature": 25.5,
  "humidity": 60.0,
  "pressure": 101325.0,
  "remarks": "设备运行正常",
  "submitTime": "2024-01-01T10:30:00"
}
```

## 故障排除

### 常见问题
1. **应用程序无法启动**
   - 检查Java版本是否为11或更高
   - 检查JavaFX运行时是否正确安装

2. **网络请求失败**
   - 检查网络连接
   - 验证配置的URL地址是否正确
   - 检查防火墙设置

3. **图片下载失败**
   - 检查设备编号是否正确
   - 验证图片服务地址是否可访问
   - 检查图片格式是否支持

4. **配置丢失**
   - 检查用户目录权限
   - 手动创建配置目录：`{用户目录}/.iot-jfx/`

### 日志查看
查看日志文件获取详细错误信息：
- 应用程序日志：`logs/application.log`
- 心跳监控日志：`logs/heartbeat.log`

## 开发说明

### 代码规范
- 使用中文注释
- 遵循Java命名规范
- 异常处理要完整
- 网络请求使用异步方式

### 扩展开发
- 添加新的表单字段：修改`form.fxml`和`FormController.java`
- 添加新的配置项：修改`AppConfig.java`和相关界面
- 添加新的网络服务：扩展`NetworkService.java`

## 版本历史

### v1.0.0
- 初始版本发布
- 实现基本的数据采集功能
- 支持设备图片下载和心跳监控
